<template>
    <div v-if="!isMobile" class="com_pro_trk_wrap_wb mb_15 boxed_width">
        <div
            v-if="
                attributes.image_side === 'left' &&
                ((isTablet && attributes.tabletMedia) ||
                    (!isTablet && attributes.media))
            "
            class="com_pt_banner"
        >
            <router-link :to="attributes.image_path">
                <img
                    v-if="!isTablet"
                    :src="attributes.media"
                    :alt="attributes.title"
                    loading="lazy"
                />
                <img
                    v-else
                    :src="attributes.tabletMedia"
                    :alt="attributes.title"
                    loading="lazy"
                />
            </router-link>
        </div>
        <div class="product_track">
            <div class="product_track_head">
                <p v-if="attributes.title" class="product_track_ttl">
                    {{ attributes.title }}
                </p>
                <router-link :to="attributes.path"
                    ><p class="product_track_btn">View All</p></router-link
                >
            </div>
            <div class="product_track_body">
                <span
                    v-if="products.length > 0"
                    style="
                        overflow-x: scroll;
                        overflow-y: hidden;
                        scrollbar-width: none;
                    "
                >
                    <div id="leftArrow" @click="moveLeft()"></div>
                    <div id="rightArrow" @click="moveRight()"></div>
                    <div class="product_track_inner" @scroll="addArrows()">
                        <div class="product_track_scroller">
                            <div
                                class="track_product fadeIn animated"
                                v-for="(product, index) in products"
                                :key="index"
                            >
                                <router-link
                                    :to="product.path"
                                    :key="product.id"
                                >
                                    <div class="track_product_image">
                                        <img
                                            :src="
                                                _.get(product, 'image') ||
                                                '/img/default_image.png'
                                            "
                                            :alt="product.title"
                                            loading="lazy"
                                        />
                                    </div>
                                    <div class="track_product_label-wrap">
                                        <p
                                            class="track_product_label"
                                            v-if="product.label"
                                            :style="`background:${product.label.color};`"
                                        >
                                            {{ product.label.name }}
                                        </p>
                                    </div>
                                    <p
                                        class="track_product_brand"
                                        v-if="product.vendor"
                                    >
                                        {{ product.vendor }}
                                    </p>
                                    <h2 class="track_product_title">
                                        {{ product.title }}
                                    </h2>
                                    <p class="track_product_price">
                                        {{ product.from_price ? 'From ' : ''
                                        }}{{
                                            product.from_price ||
                                            product.price | currency
                                        }}
                                    </p>
                                    <currency-field
                                        :price="
                                            product.from_price || product.price
                                        "
                                        :fontSize="12"
                                        :fontWeight="300"
                                    />
                                </router-link>
                            </div>
                        </div>
                    </div>
                </span>

                <track-placeholder v-else />
            </div>
        </div>

        <div
            v-if="
                attributes.image_side === 'right' &&
                ((isTablet && attributes.tabletMedia) ||
                    (!isTablet && attributes.media))
            "
            class="com_pt_banner"
        >
            <router-link :to="attributes.image_path">
                <img v-if="!isTablet" :src="attributes.media" loading="lazy" />
                <img
                    v-else
                    :src="attributes.tabletMedia || attributes.media"
                    loading="lazy"
                />
            </router-link>
        </div>
    </div>
    <div v-else>
        <mobile-track-side :attributes="attributes" :products="products" />
    </div>
</template>

<script>
import { tween } from 'popmotion';
import InlineLoader from '../partials/InlineLoader';
import MobileTrackSide from '../components/MobileTrackSide';
import CurrencyField from '../footer/CurrencyField';
import TrackPlaceholder from './TrackPlaceholder';
export default {
    components: {
        InlineLoader,
        CurrencyField,
        MobileTrackSide,
        TrackPlaceholder,
    },

    props: ['attributes'],

    data() {
        return {
            products: [],
            productWidth: 0,
            visiableWidth: 0,
            trackWidth: 0,
            track: 0,
            isTablet: false,
            isMobile: false,
            queryMediaInsTab: {},
            queryMediaInsMob: {},
        };
    },
    methods: {
        getProducts() {
            let item_count = this.isMobile ? 4 : 20;
            let query = this.attributes.path.indexOf('?') == -1 ? '?' : '&';
            query += `remove_facets=true&item_count=${item_count}`;
            axios(`/api${this.attributes.path}${query}`)
                .then((resp) => {
                    this.products = resp.data.products.data;
                })
                .catch((err) => {});
        },
        getMeasurements() {
            if (!this.$el.querySelector('.track_product')) return;

            this.productWidth =
                this.$el.querySelector('.track_product').clientWidth;
            this.visiableWidth = this.$el.querySelector(
                '.product_track_inner'
            ).clientWidth;
            this.trackWidth = this.$el.querySelector(
                '.product_track_scroller'
            ).scrollWidth;
            this.track = this.$el.querySelector('.product_track_inner');

            if (
                this.track.scrollLeft >= 0 &&
                this.track.scrollLeft != this.trackWidth - this.visiableWidth
            ) {
                let rightArrow = this.$el.querySelector('#rightArrow');
                rightArrow.classList.add('product_track_arrow', 'pta_right');
            }
        },
        moveLeft() {
            if (this.track.scrollLeft > 0) {
                let move =
                    this.visiableWidth -
                    (this.visiableWidth % this.productWidth);
                let t = tween({
                    from: Number(this.track.scrollLeft),
                    to: Number(this.track.scrollLeft - move),
                    duration: 1000,
                });
                t.start((val) => (this.track.scrollLeft = val));
            } else {
            }
        },
        moveRight() {
            if (this.track.scrollLeft != this.trackWidth - this.visiableWidth) {
                let move =
                    this.visiableWidth -
                    (this.visiableWidth % this.productWidth);
                let t = tween({
                    from: Number(this.track.scrollLeft),
                    to: Number(this.track.scrollLeft + move),
                    duration: 1000,
                });
                t.start((val) => (this.track.scrollLeft = val));
            }
        },
        addArrows() {
            let leftArrow = this.$el.querySelector('#leftArrow');
            let rightArrow = this.$el.querySelector('#rightArrow');

            if (
                this.track.scrollLeft >= 0 &&
                this.track.scrollLeft != this.trackWidth - this.visiableWidth
            ) {
                rightArrow.classList.add('product_track_arrow', 'pta_right');
            }
            if (this.track.scrollLeft > 0) {
                leftArrow.classList.add('product_track_arrow', 'pta_left');
            }
            if (this.track.scrollLeft == this.trackWidth - this.visiableWidth) {
                rightArrow.classList.remove('product_track_arrow', 'pta_right');
            }
            if (this.track.scrollLeft == 0) {
                leftArrow.classList.remove('product_track_arrow', 'pta_left');
            }
        },
        detectQueryChangeTab(e) {
            this.isTablet = !e.matches;
        },
        detectQueryChangeMob(e) {
            this.isMobile = !e.matches;
            this.getProducts();
        },
        init() {
            // for tablet
            this.queryMediaInsTab = window.matchMedia('(min-width: 959px)');
            this.isTablet = !this.queryMediaInsTab.matches;
            this.queryMediaInsTab.addListener(this.detectQueryChangeTab);

            // for mobile
            this.queryMediaInsMob = window.matchMedia('(min-width: 500px)');
            this.isMobile = !this.queryMediaInsMob.matches;
            this.queryMediaInsMob.addListener(this.detectQueryChangeMob);
        },
    },
    mounted() {
        this.init();
        this.getProducts();
    },
    updated() {
        this.getMeasurements();
    },
    watch: {
        attributes() {
            this.getProducts();
        },
    },
};
</script>
<style>
.pd-bt {
    margin-bottom: 21px;
}
</style>

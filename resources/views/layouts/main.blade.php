<!DOCTYPE html>
<html lang="en">

<head>
	<title><PERSON><PERSON><PERSON></title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="google-site-verification" content="3PHQkJoPOEO5mTNYEd0ECPi9PTbgmuXskLcZaoSMiUg" />

    <!-- Vite <PERSON>ifest for auto-linking JavaScript and CSS -->
    @vite(['resources/sass/app.scss'])

	<!-- I11 polyfills -->
	<script src="/js/ie11scripts.js"></script>

	<link rel="stylesheet" href="https://use.typekit.net/akd5lgj.css">
	<link href="https://fonts.googleapis.com/css?family=Heebo:300,400,500,700&display=swap" rel="stylesheet">
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<link rel="stylesheet" type="text/css" href="/css/slick.css">
	<script async src="/js/slick.min.js"></script>

	<meta name="csrf-token" content="{{ csrf_token() }}">

	<script type="application/ld+json">
		{
			"@context": "http://schema.org",
			"@type": "Organization",
			"name": "Eichlers",
			"url": "1800eichlers.com",
			"sameAs": [
				"https://www.instagram.com/1800eichlers/?hl=en",
				"https://facebook.com/800eichlers"
			],
			"address": {
				"@type": "PostalAddress",
				"streetAddress": "5004 13th Ave",
				"addressRegion": "NY",
				"postalCode": "11219",
				"addressCountry": "US"
			}
		}
	</script>

	<!-- Google Tag Manager -->
	<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KD8HNM8');</script>
	<!-- End Google Tag Manager -->

    @if(config('klaviyo.enabled'))
    <!-- klaviyo -->
    <script
        async type="text/javascript"
        src="//static.klaviyo.com/onsite/js/{{config('klaviyo.public_key')}}/klaviyo.js"
    ></script>
	<script>
		!function(){if(!window.klaviyo){window._klOnsite=window._klOnsite||[];try{window.klaviyo=new Proxy({},{get:function(n,i){return"push"===i?function(){var n;(n=window._klOnsite).push.apply(n,arguments)}:function(){for(var n=arguments.length,o=new Array(n),w=0;w<n;w++)o[w]=arguments[w];var t="function"==typeof o[o.length-1]?o.pop():void 0,e=new Promise((function(n){window._klOnsite.push([i].concat(o,[function(i){t&&t(i),n(i)}]))}));return e}}})}catch(n){window.klaviyo=window.klaviyo||[],window.klaviyo.push=function(){var n;(n=window._klOnsite).push.apply(n,arguments)}}}}();
		// window.klaviyo.identify({}).then(() => console.log('Identify has been completed'));
	</script>
    <!-- End klaviyo -->
    @endif
	<!-- Adroll -->
		<script type="text/javascript" src="/js/adroll.js"></script>
	<!-- End Adroll -->
</head>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-TB5RJ3SMDC');
</script>

<body>
	<div id="eichlers">
		<loading-spinner ref="spinner"></loading-spinner>
		<layouts-header></layouts-header>

		@yield('content')
		<ffooter></ffooter>
		<messaging ref="message"></messaging>
		<mobile-scroll-up ref="mobScrollUp" />
	</div>

    @vite(['resources/js/app.js'])

	@include('partials.front_end_hotjar')
	@include('partials.facebook')
	@include('vendor.helpscout.snippet')
	<script type="text/javascript" src="https://js.createsend1.com/javascript/copypastesubscribeformlogic.js"></script>
</body>
</html>
